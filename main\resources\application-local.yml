﻿server:
  port: 8080

spring:
  application:
    name: graphql-crm

  docker:
    compose:
      enabled: false

  datasource:
    username: root
    password:
    url: ***************************************

  jwks:
    uri: http://localhost:8099/oauth2/jwks

populate:
  table:
    enabled: false

management:
  endpoints:
    web:
      exposure:
        include: '*'

  endpoint:
    health:
      probes:
        enabled: true
  health:
    livenessstate:
      enabled: true
    readinessstate:
      enabled: true