package com.midas.graphqlcrm.exception;

import graphql.GraphQLError;
import graphql.schema.DataFetchingEnvironment;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.graphql.execution.ErrorType;
import org.springframework.graphql.data.method.annotation.GraphQlExceptionHandler;
import org.springframework.web.bind.annotation.ControllerAdvice;

@ControllerAdvice
public class GlobalExceptionHandler {

    @GraphQlExceptionHandler(ProcessException.class)
    public GraphQLError exception(ProcessException ex, final DataFetchingEnvironment env) {
        return GraphQLError.newError()
                .message(ex.getLocalizedMessage())
                .errorType(ErrorType.BAD_REQUEST)
                .path(env.getExecutionStepInfo().getPath())
                .location(env.getField().getSourceLocation())
                .build();
    }

    @GraphQlExceptionHandler(DataIntegrityViolationException.class)
    public GraphQLError exception(DataIntegrityViolationException ex, final DataFetchingEnvironment env) {
        return GraphQLError.newError()
                .message("Duplicate creation of entity")
                .errorType(ErrorType.BAD_REQUEST)
                .path(env.getExecutionStepInfo().getPath())
                .location(env.getField().getSourceLocation())
                .build();
    }

    @GraphQlExceptionHandler(NotFoundException.class)
    public GraphQLError exception(NotFoundException ex, final DataFetchingEnvironment env) {
        return GraphQLError.newError()
                .message(ex.getLocalizedMessage())
                .errorType(ErrorType.BAD_REQUEST)
                .path(env.getExecutionStepInfo().getPath())
                .location(env.getField().getSourceLocation())
                .build();
    }

    @GraphQlExceptionHandler(RuntimeException.class)
    public GraphQLError exception(RuntimeException ex, final DataFetchingEnvironment env) {
        return GraphQLError.newError()
                .message(ex.getLocalizedMessage())
                .errorType(ErrorType.INTERNAL_ERROR)
                .path(env.getExecutionStepInfo().getPath())
                .location(env.getField().getSourceLocation())
                .build();
    }
}

