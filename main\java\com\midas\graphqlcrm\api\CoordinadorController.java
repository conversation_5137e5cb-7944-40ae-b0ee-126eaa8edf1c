package com.midas.graphqlcrm.api;

import com.midas.graphqlcrm.entity.User;
import com.midas.graphqlcrm.service.CoordinadorService;
import com.zee.graphqlcrm.codegen.DgsConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.SchemaMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;

import java.util.List;

@Controller
@RequiredArgsConstructor
@Slf4j
public class CoordinadorController {

    private final CoordinadorService coordinadorService;

    @PreAuthorize("hasAnyAuthority('read','create')")
    @SchemaMapping(typeName = DgsConstants.MUTATION.TYPE_NAME, field = DgsConstants.MUTATION.AsignarAsesores)
    public Boolean asignarAsesores(
            @Argument(DgsConstants.MUTATION.ASIGNARASESORES_INPUT_ARGUMENT.CoordinadorId) Long coordinadorId,
            @Argument(DgsConstants.MUTATION.ASIGNARASESORES_INPUT_ARGUMENT.AsesorIds) List<Long> asesorIds) {
        return coordinadorService.asignarAsesoresACoordinador(coordinadorId, asesorIds);
    }

    @PreAuthorize("hasAnyAuthority('read','create')")
    @SchemaMapping(typeName = DgsConstants.QUERY.TYPE_NAME, field = DgsConstants.QUERY.AsesoresSinCoordinador)
    public List<com.zee.graphqlcrm.codegen.types.User> getAsesoresSinCoordinador() {
        return coordinadorService.obtenerAsesoresSinCoordinador().stream()
                .map(this::mapToUserDto)
                .collect(java.util.stream.Collectors.toList());
    }

    @PreAuthorize("hasAnyAuthority('read','create')")
    @SchemaMapping(typeName = DgsConstants.QUERY.TYPE_NAME, field = DgsConstants.QUERY.AsesoresDeCoordinador)
    public List<com.zee.graphqlcrm.codegen.types.User> getAsesoresDeCoordinador(
            @Argument(DgsConstants.QUERY.ASESORESDECOORDINADOR_INPUT_ARGUMENT.CoordinadorId) Long coordinadorId) {
        return coordinadorService.obtenerAsesoresDeCoordinador(coordinadorId).stream()
                .map(this::mapToUserDto)
                .collect(java.util.stream.Collectors.toList());
    }

    private com.zee.graphqlcrm.codegen.types.User mapToUserDto(User user) {
        return com.zee.graphqlcrm.codegen.types.User.newBuilder()
                .id(user.getId().toString())
                .username(user.getUsername())
                .nombre(user.getNombre())
                .apellido(user.getApellido())
                .dni(user.getDni())
                .telefono(user.getTelefono())
                .email(user.getEmail())
                .fechaCreacion(
                        user.getFechaCreacion() != null ? user.getFechaCreacion().atOffset(java.time.ZoneOffset.UTC)
                                : null)
                .estado(user.getEstado())
                .role(com.zee.graphqlcrm.codegen.types.Role.valueOf(user.getRole().name()))
                .build();
    }
}
