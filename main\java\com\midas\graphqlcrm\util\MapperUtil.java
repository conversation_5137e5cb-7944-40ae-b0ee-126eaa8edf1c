package com.midas.graphqlcrm.util;

import com.zee.graphqlcrm.codegen.types.User;
import com.zee.graphqlcrm.codegen.types.UserInput;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Component;

import java.time.ZoneOffset;

/**
 * Clase de utilidades para mapear entre entidades y tipos GraphQL.
 */
@Component
public class MapperUtil {

    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    /**
     * Convierte un input GraphQL (UserInput) a una entidad JPA (User).
     *
     * @param input objeto recibido desde GraphQL
     * @return entidad User persistible
     */
    public com.midas.graphqlcrm.entity.User mapToUserEntity(final UserInput input) {
        com.midas.graphqlcrm.entity.User user = new com.midas.graphqlcrm.entity.User();

        user.setUsername(input.getUsername());
        user.setPassword(passwordEncoder.encode(input.getPassword())); // Encriptar contraseña
        user.setNombre(input.getNombre());
        user.setApellido(input.getApellido());
        user.setDni(input.getDni());
        user.setTelefono(input.getTelefono());
        user.setEmail(input.getEmail());
        user.setRole(com.midas.graphqlcrm.entity.Role.valueOf(input.getRole().name()));
        user.setEstado("A"); // Estado activo por defecto

        return user;
    }

    /**
     * Convierte una entidad JPA (User) a un objeto GraphQL (User DTO).
     *
     * @param user entidad JPA obtenida de la base de datos
     * @return objeto GraphQL User para ser expuesto en la API
     */
    public User mapToUserDto(final com.midas.graphqlcrm.entity.User user) {
        return User.newBuilder()
                .id(user.getId().toString())
                .username(user.getUsername())
                .nombre(user.getNombre())
                .apellido(user.getApellido())
                .dni(user.getDni())
                .telefono(user.getTelefono())
                .email(user.getEmail())
                .fechaCreacion(user.getFechaCreacion() != null
                        ? user.getFechaCreacion().atOffset(ZoneOffset.UTC)
                        : null)
                .fechaCese(user.getFechaCese() != null
                        ? user.getFechaCese().atStartOfDay().atOffset(ZoneOffset.UTC)
                        : null)
                .estado(user.getEstado())
                .role(com.zee.graphqlcrm.codegen.types.Role.valueOf(user.getRole().name()))
                .build();
    }
}
