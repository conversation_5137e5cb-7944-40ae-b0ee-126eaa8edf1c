package com.midas.graphqlcrm.config;

import graphql.analysis.MaxQueryComplexityInstrumentation;
import graphql.analysis.MaxQueryDepthInstrumentation;
import graphql.execution.instrumentation.Instrumentation;
import graphql.execution.instrumentation.tracing.TracingInstrumentation;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class InstrumentationConfig {

    @Bean
    public Instrumentation tracingInstrumentation() {
        return new TracingInstrumentation();
    }

    /**
     * Limits the number of fields a query response body can have
     * hence restricting caller to certain number of fields
     * note ** tracingInstrumentation will increase this
     */
    @Bean
    public Instrumentation maxQueryComplexityInstrumentation() {
        return new MaxQueryComplexityInstrumentation(19);
    }

    @Bean
    Instrumentation maxQueryInstrumentation() {
        return new MaxQueryDepthInstrumentation(7);
    }
}
