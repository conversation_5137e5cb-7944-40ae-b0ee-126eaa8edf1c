import { Injectable } from '@angular/core';
import { Apollo, gql } from 'apollo-angular';

@Injectable({ providedIn: 'root' })
export class AuthService {
  constructor(private apollo: Apollo) {}

  login(username: string, password: string) {
    return this.apollo.mutate({
      mutation: gql`
        mutation Login($input: LoginInput!) {
          login(input: $input) {
            success
            message
            token
          }
        }
      `,
      variables: {
        input: { username, password },
      },
    });
  }
}
