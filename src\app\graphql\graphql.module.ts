import { NgModule } from '@angular/core';
import { APOLLO_OPTIONS, ApolloModule } from 'apollo-angular';
import {
  ApolloClient,
  InMemoryCache,
  split,
  HttpLink,
} from '@apollo/client/core';
import { GraphQLWsLink } from '@apollo/client/link/subscriptions';
import { createClient } from 'graphql-ws';
import { getMainDefinition } from '@apollo/client/utilities';

const httpUri = 'http://localhost:8080/graphql';
const wsUri = 'ws://localhost:8080/subscription';

export function createApollo(): ApolloClient<any> {
  const http = new HttpLink({ uri: httpUri });

  const ws = new GraphQLWsLink(createClient({ url: wsUri }));

  const link = split(
    ({ query }) => {
      const def = getMainDefinition(query);
      return (
        def.kind === 'OperationDefinition' && def.operation === 'subscription'
      );
    },
    ws,
    http
  );

  return new ApolloClient({
    link,
    cache: new InMemoryCache(),
  });
}

@NgModule({
  exports: [ApolloModule],
  providers: [
    {
      provide: APOLLO_OPTIONS,
      useFactory: createApollo,
    },
  ],
})
export class GraphQLModule {}
