<div class="flex items-center justify-center min-h-screen bg-gray-100">
    <div class="bg-white p-8 rounded shadow-md w-full max-w-sm">
      <h2 class="text-2xl font-bold mb-4 text-center">Iniciar se<PERSON><PERSON></h2>
      <input [(ngModel)]="username" placeholder="Usuario" class="mb-2 p-2 border w-full rounded" />
      <input [(ngModel)]="password" type="password" placeholder="Contraseña" class="mb-4 p-2 border w-full rounded" />
      <button (click)="login()" class="bg-blue-600 text-white w-full p-2 rounded hover:bg-blue-800">
        Entrar
      </button>
      <p *ngIf="error" class="text-red-500 mt-2 text-sm">{{ error }}</p>
    </div>
  </div>
  