import { Component, OnInit } from '@angular/core';
import { UserService } from '../user.service';
import { User } from '../user.model';

@Component({
  selector: 'app-user-list',
  templateUrl: './user-list.component.html',
})
export class UserListComponent implements OnInit {
  users: User[] = [];

  constructor(private userService: UserService) {}

  ngOnInit() {
    this.userService.getAllUsers().subscribe((res) => {
      this.users = res.getAllUsers;
    });

    this.userService.userListUpdates().subscribe((update) => {
      this.users = update.userListUpdates;
    });
  }
}
