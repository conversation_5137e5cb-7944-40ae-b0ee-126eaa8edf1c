package com.midas.graphqlcrm.repository;

import com.midas.graphqlcrm.entity.Role;
import com.midas.graphqlcrm.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, Long>, JpaSpecificationExecutor<User> {

    /* ---------- BÚSQUEDAS BÁSICAS ---------- */
    Optional<User> findByUsername(String username);

    Optional<User> findByEmail(String email);

    Optional<User> findByDni(String dni);

    boolean existsByUsername(String username);

    boolean existsByDni(String dni);

    boolean existsByEmail(String email);

    /* ---------- PAGINADAS / FILTRADAS ---------- */
    Page<User> findAllByRole(Role role, Pageable pageable);

    /* ---------- BÚSQUEDAS POR ESTADO ---------- */
    Page<User> findAllByEstado(String estado, Pageable pageable);

    Page<User> findAllByRoleAndEstado(Role role, String estado, Pageable pageable);

    /* ---------- RELACIONES COORDINADOR-ASESOR ---------- */
    List<User> findByCoordinadorId(Long coordinadorId);

    List<User> findByCoordinadorIdAndRole(Long coordinadorId, Role role);

    List<User> findByRoleAndCoordinadorIsNull(Role role);

    // Método para cargar usuario con su coordinador (evita
    // LazyInitializationException)
    @Query("SELECT u FROM User u LEFT JOIN FETCH u.coordinador WHERE u.id = :id")
    User findByIdWithCoordinador(@Param("id") Long id);
}
