package com.midas.graphqlcrm.service;

import com.midas.graphqlcrm.entity.Role;
import com.midas.graphqlcrm.entity.User;
import com.midas.graphqlcrm.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class CoordinadorService {

    private final UserRepository userRepository;

    @Transactional(readOnly = true)
    public List<User> obtenerAsesoresSinCoordinador() {
        return userRepository.findByRoleAndCoordinadorIsNull(Role.ASESOR);
    }

    @Transactional(readOnly = true)
    public List<User> obtenerAsesoresDeCoordinador(Long coordinadorId) {
        return userRepository.findByCoordinadorIdAndRole(coordinadorId, Role.ASESOR);
    }

    @Transactional
    public boolean asignarAsesoresACoordinador(Long coordinadorId, List<Long> asesorIds) {
        try {
            // Verificar que el coordinador existe
            User coordinador = userRepository.findById(coordinadorId)
                    .orElseThrow(() -> new RuntimeException("Coordinador no encontrado"));

            // Verificar que el usuario es un coordinador
            if (coordinador.getRole() != Role.COORDINADOR) {
                throw new RuntimeException("El usuario no es un coordinador");
            }

            // Obtener los asesores
            List<User> asesores = userRepository.findAllById(asesorIds);
            if (asesores.size() != asesorIds.size()) {
                throw new RuntimeException("Algunos asesores no fueron encontrados");
            }

            // Verificar que todos son asesores y asignarles el coordinador
            for (User asesor : asesores) {
                if (asesor.getRole() != Role.ASESOR) {
                    throw new RuntimeException("El usuario " + asesor.getUsername() + " no es un asesor");
                }
                asesor.setCoordinador(coordinador);
            }

            // Guardar los cambios
            userRepository.saveAll(asesores);

            log.info("Asesores asignados exitosamente al coordinador {}", coordinador.getUsername());
            return true;

        } catch (Exception e) {
            log.error("Error al asignar asesores al coordinador: {}", e.getMessage());
            return false;
        }
    }
}
