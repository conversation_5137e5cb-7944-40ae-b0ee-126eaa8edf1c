package com.midas.graphqlcrm.search.user;

import com.midas.graphqlcrm.entity.User;
import com.midas.graphqlcrm.search.SpecUtil;
import com.zee.graphqlcrm.codegen.types.Role;
import org.springframework.data.jpa.domain.Specification;

public final class UserSearchSpecs {

    private UserSearchSpecs() {}

    public static Specification<User> idNotNull() {
        return (entity, cq, cb) -> cb.isNotNull(entity.get("id"));
    }

    public static Specification<User> usernameContains(String username) {
        return (entity, cq, cb) -> cb.like(cb.lower(entity.get("username")), SpecUtil.contains(username.toLowerCase()));
    }

    public static Specification<User> nombreContains(String nombre) {
        return (entity, cq, cb) -> cb.like(cb.lower(entity.get("nombre")), SpecUtil.contains(nombre.toLowerCase()));
    }

    public static Specification<User> apellidoContains(String apellido) {
        return (entity, cq, cb) -> cb.like(cb.lower(entity.get("apellido")), SpecUtil.contains(apellido.toLowerCase()));
    }

    public static Specification<User> dniEquals(String dni) {
        return (entity, cq, cb) -> cb.equal(entity.get("dni"), dni);
    }

    public static Specification<User> emailContains(String email) {
        return (entity, cq, cb) -> cb.like(cb.lower(entity.get("email")), SpecUtil.contains(email.toLowerCase()));
    }

    public static Specification<User> telefonoContains(String telefono) {
        return (entity, cq, cb) -> cb.like(entity.get("telefono"), SpecUtil.contains(telefono));
    }

    public static Specification<User> estadoEquals(String estado) {
        return (entity, cq, cb) -> cb.equal(entity.get("estado"), estado);
    }

    public static Specification<User> roleEquals(Role role) {
        return (entity, cq, cb) -> cb.equal(entity.get("role"), role);
    }

    public static Specification<User> usernameEquals(String username) {
        return (entity, cq, cb) -> cb.equal(entity.get("username"), username);
    }

    public static Specification<User> idEquals(Long id) {
        return (entity, cq, cb) -> cb.equal(entity.get("id"), id);
    }

    public static Specification<User> activeUsers() {
        return (entity, cq, cb) -> cb.equal(entity.get("estado"), "A");
    }
}
