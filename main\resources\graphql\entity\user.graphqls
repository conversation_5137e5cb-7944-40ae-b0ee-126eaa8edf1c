type User {
  id: ID!
  username: String!
  nombre: String!
  apellido: String!
  dni: String!
  telefono: String
  email: String
  fechaCreacion: DateTime
  fechaCese: DateTime
  estado: String!
  role: Role!
}

input UserInput {
  username: String!
  password: String!
  nombre: String!
  apellido: String!
  dni: String!
  telefono: String
  email: String
  role: Role!
}

input LoginInput {
  username: String!
  password: String!
}

input UserSearchInput {
  username: String
  nombre: String
  apellido: String
  dni: String
  email: String
  telefono: String
  estado: String
  role: Role
}

type AuthResponse {
  success: Boolean!
  message: String!
  token: String
  user: User
}

type LoginResponse {
  success: Boolean!
  message: String!
  token: String
  user: UserLoginData
}

type UserLoginData {
  id: ID!
  username: String!
  nombre: String!
  apellido: String!
  dni: String!
  telefono: String
  email: String
  role: Role!
  estado: String!
  sede_id: String
  sede: String
  asesores: [AsesorData]
  coordinador: CoordinadorData
}

type AsesorData {
  id: String!
  username: String!
  nombre: String!
  apellido: String!
  dni: String!
  telefono: String
  email: String
}

type CoordinadorData {
  id: String!
  username: String!
  nombre: String!
  apellido: String!
  dni: String!
  telefono: String
  email: String
}

type RegisterResponse {
  success: Boolean!
  message: String!
  user: User
}
