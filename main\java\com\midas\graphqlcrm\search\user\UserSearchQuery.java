package com.midas.graphqlcrm.search.user;

import com.midas.graphqlcrm.entity.User;
import com.zee.graphqlcrm.codegen.types.UserSearchInput;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
public class UserSearchQuery {

    public static final String USER_ID = "id";

    public Specification<User> buildUserSearchParams(UserSearchInput input) {
        Specification<User> specs = UserSearchSpecs.idNotNull();

        if (StringUtils.hasText(input.getUsername())) {
            specs = specs.and(UserSearchSpecs.usernameContains(input.getUsername()));
        }

        if (StringUtils.hasText(input.getNombre())) {
            specs = specs.and(UserSearchSpecs.nombreContains(input.getNombre()));
        }

        if (StringUtils.hasText(input.getApellido())) {
            specs = specs.and(UserSearchSpecs.apellidoContains(input.getApellido()));
        }

        if (StringUtils.hasText(input.getDni())) {
            specs = specs.and(UserSearchSpecs.dniEquals(input.getDni()));
        }

        if (StringUtils.hasText(input.getEmail())) {
            specs = specs.and(UserSearchSpecs.emailContains(input.getEmail()));
        }

        if (StringUtils.hasText(input.getTelefono())) {
            specs = specs.and(UserSearchSpecs.telefonoContains(input.getTelefono()));
        }

        if (StringUtils.hasText(input.getEstado())) {
            specs = specs.and(UserSearchSpecs.estadoEquals(input.getEstado()));
        }

        if (input.getRole() != null) {
            specs = specs.and(UserSearchSpecs.roleEquals(input.getRole()));
        }

        return specs;
    }

    public Specification<User> buildUserByUsername(String username) {
        return UserSearchSpecs.usernameEquals(username);
    }

    public Specification<User> buildUserById(Long id) {
        return UserSearchSpecs.idEquals(id);
    }

    public Specification<User> buildActiveUsers() {
        return UserSearchSpecs.activeUsers();
    }
}
