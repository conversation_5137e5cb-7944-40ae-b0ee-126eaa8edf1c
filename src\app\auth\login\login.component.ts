import { Component } from '@angular/core';
import { AuthService } from '../auth.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
})
export class LoginComponent {
  username = '';
  password = '';
  error = '';

  constructor(private authService: AuthService, private router: Router) {}

  login() {
    this.authService.login(this.username, this.password).subscribe({
      next: (result) => {
        if (result.login.success) {
          localStorage.setItem('token', result.login.token);
          this.router.navigate(['/users']);
        } else {
          this.error = result.login.message;
        }
      },
      error: () => (this.error = 'Error de conexión'),
    });
  }
}
