package com.midas.graphqlcrm.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "usuarios", indexes = {
        @Index(name = "idx_username", columnList = "usuario", unique = true),
        @Index(name = "idx_dni", columnList = "dni", unique = true),
        @Index(name = "idx_email", columnList = "email", unique = true)
})
@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
public class User {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "usuario", nullable = false, length = 50, unique = true)
    private String username;

    @Column(name = "clave", nullable = false, length = 200)
    private String password;

    @Column(name = "nombre", nullable = false)
    private String nombre;

    @Column(name = "apellido", nullable = false)
    private String apellido;

    @Column(name = "dni", nullable = false, length = 8, unique = true)
    private String dni;

    @Column(name = "telefono", nullable = true)
    private String telefono;

    @Column(name = "email", nullable = true, length = 50, unique = true)
    private String email;

    @CreatedDate
    @Column(name = "fecha_creacion", nullable = false)
    private LocalDateTime fechaCreacion = LocalDateTime.now();

    @Column(name = "fecha_cese")
    private LocalDate fechaCese;

    @Column(name = "estado", nullable = false, length = 1)
    private String estado = "A"; // Valor por defecto

    @Enumerated(EnumType.STRING)
    @Column(name = "role", nullable = false)
    private Role role = Role.ASESOR;

    @Transient
    private String token;

    @JsonProperty("tokenPassword")
    @Column(name = "tokenPassword", nullable = true)
    private String tokenPassword;

    // Relaciones coordinador-asesor
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "coordinador_id")
    @JsonBackReference // Evita la serialización inversa para evitar ciclo
    @JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
    private User coordinador;

    @OneToMany(mappedBy = "coordinador", fetch = FetchType.LAZY)
    @JsonManagedReference // Serializa esta colección sin volver a serializar el coordinador
    @JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
    private List<User> asesores;

    // Constructor con ID para referencias
    public User(Long id) {
        this.id = id;
    }

    @LastModifiedDate
    @Column(name = "deletion_time", nullable = true)
    private LocalDateTime deletionTime;

    @LastModifiedDate
    private Timestamp updatedAt;
}
