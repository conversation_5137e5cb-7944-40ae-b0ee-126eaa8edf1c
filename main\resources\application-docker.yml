﻿spring:
  application:
    name: graphql-crm

  datasource:
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    url: jdbc:mysql://${HOST}}:${PORT}}/${DB_NAME}

  jwks:
    uri: http://${AUTH_SERVER_HOST}:${AUTH_SERVER_PORT}/oauth2/jwks

populate:
  table:
    enabled: false

management:
  endpoints:
    web:
      exposure:
        include: ${ACTUATOR_EXPOSED_ENDPOINT:health}

  endpoint:
    health:
      probes:
        enabled: true
  health:
    livenessstate:
      enabled: true
    readinessstate:
      enabled: true